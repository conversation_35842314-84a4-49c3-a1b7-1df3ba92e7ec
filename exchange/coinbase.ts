import path from 'path'
import crypto from 'crypto'
import jwt from 'jsonwebtoken'
import settings from '../settings';
import qs from 'qs'
import { TRADEING_LEVERAGE } from '../common/constants';

const generateJWT = (uri: string): string => {
    const algorithm = 'ES256'
    const payload = {
        iss: 'cdp',
        nbf: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 120,
        sub: settings.exchange.coinbase.credential.name,
        uri,
    };

    const header = {
        alg: algorithm,
        kid: settings.exchange.coinbase.credential.name,
        nonce: crypto.randomBytes(16).toString('hex'),
    };

    return jwt.sign(payload, settings.exchange.coinbase.credential.privateKey, { algorithm, header });
};



async function callApi(reqPath: string, params?: { method?: string, body?: any, query?: any }) {
    const message = `${params?.method || 'GET'} ${path.basename(settings.exchange.coinbase.apiUrl)}${reqPath}`;
    const sign = generateJWT(message);
    const query = params?.query ? qs.stringify(params.query) : '';
    const url = `${settings.exchange.coinbase.apiUrl}${reqPath}${query ? `?${query}` : ''}`;

    const res = await fetch(url, {
        method: params?.method || 'GET',
        headers: {
            Authorization: `Bearer ${sign}`,
            'Content-Type': 'application/json'
        },
        proxy: settings.proxy,
        body: params?.body ? JSON.stringify(params.body) : undefined
    });

    if (res.status !== 200) {
        const text = await res.text();
        throw new Error(text);
    }

    return res.json();
}

export async function fetchPortfolioId() {
    const data = await callApi('/api/v3/brokerage/portfolios', {
        method: 'GET',
        query: { portfolio_type: 'INTX' }
    });
    return data.portfolios[0]?.uuid;
}

export async function fetchPositionList() {
    const data = await callApi(`/api/v3/brokerage/intx/positions/${settings.exchange.coinbase.portfolioId}`, {
        method: 'GET',
    });
    return data
}


export async function placeOrder(symbol: string, side: string, cryptoAmount: string) {
    const reqPath = '/api/v3/brokerage/orders';
    const reqMethod = 'POST'
    const orderId = Bun.randomUUIDv7()
    const body = {
        "client_order_id": orderId,
        "product_id": `${symbol.toLowerCase().replace('usdt', '').toUpperCase()}-PERP-INTX`,
        "side": side.toUpperCase(),
        "order_configuration": {
            "marketMarketIoc": {
                "baseSize": cryptoAmount
            }
        }, "leverage": TRADEING_LEVERAGE.toString(),
        "margin_type": "CROSS"
    }
    const data = callApi(reqPath, {
        method: reqMethod,
        body
    })
    return data
}

export async function closePosition(symbol: string) {
    const reqPath = '/api/v3/brokerage/orders/close_position';
    const reqMethod = 'POST'


    const orderId = Bun.randomUUIDv7()
    const body = {
        "client_order_id": orderId,
        "product_id": `${symbol.toLowerCase().replace('usdt', '').toUpperCase()}-PERP-INTX`,
    }
    const data = callApi(reqPath, {
        method: reqMethod,
        body
    })
    return data
}

export async function fetchProduct(symbol: string) {
    const reqPath = `/api/v3/brokerage/market/products/${symbol.toLowerCase().replace('usdt', '').toUpperCase()}-PERP-INTX`
    const reqMethod = 'GET'
    const data = await callApi(reqPath, {
        method: reqMethod,
    });
    return data
}


export async function fetchAccountList() {
    const reqPath = `/api/v3/brokerage/accounts`
    const reqMethod = 'GET'
    const data = await callApi(reqPath, {
        method: reqMethod,
    });
    return data
}