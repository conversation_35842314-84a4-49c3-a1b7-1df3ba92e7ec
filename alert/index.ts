import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>rig<PERSON>, TAlertConfig } from "../types/alertConfig.types";
import { type AlertArgReader } from "./argReader";
import cache from "../common/cache";

export class Alert {
    private alertConfig: TAlertConfig;
    private argReader: AlertArgReader;

    constructor(alertConfig: TAlertConfig, argReader: AlertArgReader) {
        this.alertConfig = alertConfig;
        this.argReader = argReader
    }

    static async isTriggable(alertConfig: TAlertConfig, startTime: number, endTime: number) {
        switch (alertConfig.triggerType) {
            case EAlertTrigger.BeforeClosed: {
                const diff = endTime - Date.now()
                return diff >= 0 && diff <= 20 * 1000
            }
            case EAlertTrigger.Once: {
                const key = `alert:${alertConfig.id}`;
                if (!(cache.getValues(key))) {
                    return true;
                }
                return false;
            }
            case EAlertTrigger.OncePerCandle: {
                const key = `alert:${alertConfig.id}:${startTime.toString()}`;
                if (!cache.exists(key)) {
                    return true;
                }
                return false;
            }
            default:
                return false;
        }
    }


    evalute() {
        const { op } = this.alertConfig
        const data1 = this.argReader.get(this.alertConfig.arg1);
        const data2 = this.argReader.get(this.alertConfig.arg2);
        switch (op.type) {
            case EAlertOp.Cross:
                return this.cross(data1 as number[], data2 as number[])
            case EAlertOp.CrossUp:
                return this.crossUp(data1 as number[], data2 as number[])
            case EAlertOp.CrossDown:
                return this.crossDown(data1 as number[], data2 as number[])
            case EAlertOp.CrossChannel:
                return this.crossChannel(data1 as number[], data2 as number[][])
            case EAlertOp.EnterChannel:
                return this.enterChannel(data1 as number[], data2 as number[][])
            case EAlertOp.ExitChannel:
                return this.exitChannel(data1 as number[], data2 as number[][])
            default:
                return false
        }
    }

    cross(data1: number[], data2: number[]) {
        if (data1.length !== data2.length || data1.length !== 2) return false
        const [y1, y2] = data1;
        const [y3, y4] = data2;
        return ((y1 - y3) * (y2 - y4)) <= 0;
    }

    crossUp(data1: number[], data2: number[]) {
        if (data1.length !== data2.length || data1.length !== 2) return false
        const [y1, y2] = data1;
        const [y3, y4] = data2;
        return (y1 - y3) <= 0 && (y2 - y4) >= 0;
    }
    crossDown(data1: number[], data2: number[]) {
        if (data1.length !== data2.length || data1.length !== 2) return false
        const [y1, y2] = data1;
        const [y3, y4] = data2;
        return (y1 - y3) >= 0 && (y2 - y4) <= 0;
    }

    crossChannel(data1: number[], data2: number[][]) {
        // Basic input validation
        if (data1.length !== 2 || data2.length !== 2 || data2[0].length !== 2 || data2[1].length !== 2) {
            console.warn("Invalid input for crossChannel. Expected data1 as [y1, y2] and data2 as [[y3, y4], [y5, y6]].");
            return false;
        }

        const [y1, y2] = data1;
        const [y3, y4] = data2[0]; // First channel line (e.g., lower bound)
        const [y5, y6] = data2[1]; // Second channel line (e.g., upper bound)

        // Checks if the segment (y1, y2) crosses the line (y3, y4) OR the line (y5, y6).
        // The product of differences being less than or equal to 0 means the points are on opposite sides of the line, or one is on the line.
        const crossesLine1 = (y1 - y3) * (y2 - y4) <= 0;
        const crossesLine2 = (y1 - y5) * (y2 - y6) <= 0;

        return crossesLine1 || crossesLine2;
    }
    enterChannel(data1: number[], data2: number[][]) {
        if (data1.length !== 2 || data2.length !== 2 || data2[0].length !== 2 || data2[1].length !== 2) {
            console.warn("Invalid input for enterChannel. Expected data1 as [y1, y2] and data2 as [[y3, y4], [y5, y6]].");
            return false;
        }

        const [y1, y2] = data1; // y1 is previous point, y2 is current point
        const [minChannelY, maxChannelY] = getChannelBounds(data2);

        const wasOutside = !isPointInsideChannel(y1, minChannelY, maxChannelY);
        const nowInside = isPointInsideChannel(y2, minChannelY, maxChannelY);

        // To enter, the previous point must be outside, and the current point must be inside.
        // Also, the segment must have crossed one of the channel boundaries.
        return wasOutside && nowInside && this.crossChannel(data1, data2);
    }
    exitChannel(data1: number[], data2: number[][]) {
        if (data1.length !== 2 || data2.length !== 2 || data2[0].length !== 2 || data2[1].length !== 2) {
            console.warn("Invalid input for exitChannel. Expected data1 as [y1, y2] and data2 as [[y3, y4], [y5, y6]].");
            return false;
        }

        const [y1, y2] = data1; // y1 is previous point, y2 is current point
        const [minChannelY, maxChannelY] = getChannelBounds(data2);

        const wasInside = isPointInsideChannel(y1, minChannelY, maxChannelY);
        const nowOutside = !isPointInsideChannel(y2, minChannelY, maxChannelY);

        // To exit, the previous point must be inside, and the current point must be outside.
        // Also, the segment must have crossed one of the channel boundaries.
        return wasInside && nowOutside && this.crossChannel(data1, data2);
    }
}

function isPointInsideChannel(pointY: number, minChannelY: number, maxChannelY: number): boolean {
    return pointY >= minChannelY && pointY <= maxChannelY;
}

// Helper function to get the min and max Y values of the channel
function getChannelBounds(data2: number[][]): [number, number] {
    const [channelLine1, channelLine2] = data2;
    const channelYs = [...channelLine1, ...channelLine2];
    const minChannelY = Math.min(...channelYs);
    const maxChannelY = Math.max(...channelYs);
    return [minChannelY, maxChannelY];
}