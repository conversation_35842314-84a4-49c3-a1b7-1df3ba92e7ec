import { db } from "./sqlite";
import { TNewTradeOrder, TTradeOrder, EOrderSide, EPositionSide } from "../types/tradeOrder.types";


export function findOpenPosition(symbol: string) {
    const query = `
        SELECT * FROM trade_order
        WHERE symbol = ?
        ORDER BY createdAt DESC
        LIMIT 1
    `;
    const order = db.query(query).get(symbol) as TTradeOrder
    if (!order) return null
    if (order.isClose) return null
    return {
        symbol: order.symbol,
        side: order.side === EOrderSide.BUY ? EPositionSide.LONG : EPositionSide.SHORT
    }

}

export function insertOrder(newOrder: TNewTradeOrder) {
    const queryStr = `INSERT INTO trade_order (symbol, orderId, side, isClose, createdAt) VALUES (?, ?,?, ?, ?)`
    db.run(queryStr, [newOrder.symbol, newOrder.orderId, newOrder.side, newOrder.isClose ?? 0, Date.now()])
}

export function findOrders(params: {
    symbol?: string;
    side?: EOrderSide;
    isClose?: number;
    limit?: number;
}) {
    let query = 'SELECT * FROM trade_order';
    const conditions: string[] = [];
    const values: any[] = [];

    if (params.symbol) {
        conditions.push('symbol = ?');
        values.push(params.symbol);
    }

    if (params.side) {
        conditions.push('side = ?');
        values.push(params.side);
    }

    if (params.isClose !== undefined) {
        conditions.push('isClose = ?');
        values.push(params.isClose);
    }

    if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY createdAt DESC';

    if (params.limit) {
        query += ` LIMIT ${params.limit}`;
    }

    const rows = db.query(query).all(...values) as TTradeOrder[];
    return rows;
}