import { Database } from 'bun:sqlite';
import { SymbolList, TimeframeList } from '../common/constants';
import settings from '../settings';

const db = new Database(settings.db.app.url);
db.run("PRAGMA busy_timeout = 5000");
db.run("PRAGMA journal_mode=WAL;");

function generateOhlcvCreateTableSQL(symbol: string, timeframe: string): string {
    const tableName = `ohlcv_${symbol}_${timeframe}`;
    return `
        CREATE TABLE IF NOT EXISTS ${tableName} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            time INTEGER NOT NULL UNIQUE,
            open REAL NOT NULL,
            high REAL NOT NULL,
            low REAL NOT NULL,
            close REAL NOT NULL,
            volume REAL NOT NULL
        );
    `;
}

function generateAggTradeTableSQL(symbol: string): string {
    const tableName = `agg_trade_${symbol}`;
    return `
         CREATE TABLE IF NOT EXISTS ${tableName} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            aggId INTEGER NOT NULL UNIQUE,
            price REAL NOT NULL,
            qty REAL NOT NULL,
            time INTEGER NOT NULL,
            isMaker INTEGER NOT NULL DEFAULT 0
        );
        CREATE INDEX IF NOT EXISTS idx_${tableName}_aggId_time ON ${tableName}(aggId,time);
        CREATE INDEX IF NOT EXISTS idx_${tableName}_time ON ${tableName}(time);
        CREATE INDEX IF NOT EXISTS idx_${tableName}_time_price_qty ON ${tableName}(time, price, qty);
    `;
}

function createNoteTableSql() {
    return `
        CREATE TABLE IF NOT EXISTS note (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            drawingId TEXT NOT NULL,
            content TEXT NOT NULL
        );
    `;
}

function createAlertConfigTableSql() {
    return `
        CREATE TABLE IF NOT EXISTS alert_config (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT,
            timeframe TEXT,
            arg1 TEXT NOT NULL,
            arg2 TEXT,
            enabled INTEGER NOT NULL DEFAULT 0,
            triggerType TEXT NOT NULL,
            op TEXT NOT NULL,
            muted INTEGER NOT NULL DEFAULT 0,
            general INTEGER NOT NULL DEFAULT 0,
            chart TEXT,
            createdAt INTEGER NOT NULL
        );
        CREATE INDEX IF NOT EXISTS idx_alert_config_symbol_timeframe ON alert_config(symbol, timeframe);
    `;
}

function createDrawingConfigTableSql() {
    return `
        CREATE TABLE IF NOT EXISTS drawing_config (
           id INTEGER PRIMARY KEY AUTOINCREMENT,
            timeframe TEXT NOT NULL,
            symbol TEXT NOT NULL,
            chart TEXT NOT NULL,
            type TEXT NOT NULL,
            dataPoints TEXT NOT NULL,
            createdAt INTEGER NOT NULL
        );
        CREATE INDEX IF NOT EXISTS idx_drawing_config_symbol_chart ON drawing_config(symbol, chart);
    `;
}

function createOrderTableSql() {
    return `
        CREATE TABLE IF NOT EXISTS trade_order (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            orderId TEXT NOT NULL,
            side TEXT NOT NULL,
            isClose INTEGER NOT NULL DEFAULT 0,
            createdAt INTEGER NOT NULL
        );
        CREATE INDEX IF NOT EXISTS idx_symbol_side_isClose_created_at ON trade_order(symbol, side, isClose, createdAt);
    `;
}

function createOrderTriggerTableSql() {
    return `
        CREATE TABLE IF NOT EXISTS order_trigger (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            triggerType TEXT NOT NULL,
            triggerPrice REAL NOT NULL,
            orderSide TEXT,
            usdAmount REAL,
            status TEXT NOT NULL DEFAULT 'active',
            createdAt INTEGER NOT NULL,
            triggeredAt INTEGER,
            orderId TEXT
        );
        CREATE INDEX IF NOT EXISTS idx_order_trigger_symbol_status ON order_trigger(symbol, status);
        CREATE INDEX IF NOT EXISTS idx_order_trigger_status_trigger_price ON order_trigger(status, triggerPrice);
    `;
}

// function createAggTradeTableSql() {
//     return `
//         CREATE TABLE IF NOT EXISTS agg_trade (
//             id INTEGER PRIMARY KEY AUTOINCREMENT,
//             symbol TEXT NOT NULL,
//             aggId INTEGER NOT NULL UNIQUE,
//             price REAL NOT NULL,
//             qty REAL NOT NULL,
//             time INTEGER NOT NULL,
//             isMaker INTEGER NOT NULL DEFAULT 0
//         );
//         CREATE INDEX IF NOT EXISTS idx_agg_trade_symbol_aggId ON agg_trade(symbol, aggId);
//         CREATE INDEX IF NOT EXISTS idx_agg_trade_symbol_time ON agg_trade(symbol, time);
//     `;
// }

function createTables() {
    for (const symbol of SymbolList) {
        const createAggTradeTable = generateAggTradeTableSQL(symbol)
        db.run(createAggTradeTable);
        for (const timeframe of TimeframeList) {
            const createTableSQL = generateOhlcvCreateTableSQL(symbol, timeframe);
            db.run(createTableSQL);
        }
    }
    db.run(createNoteTableSql());
    db.run(createAlertConfigTableSql());
    db.run(createDrawingConfigTableSql());
    db.run(createOrderTableSql());
    db.run(createOrderTriggerTableSql());
    // db.run(createAggTradeTableSql());
}

createTables();

export { db }