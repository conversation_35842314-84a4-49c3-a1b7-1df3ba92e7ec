import { db } from "./sqlite";

import { TAggTrade, TAggTradeGap } from "../types/aggTrade.types";
import { SymbolList, TimeframeMillsMap } from "../common/constants";

export function findGaps(): TAggTradeGap[] {

    const to = Date.now()
    const from = to - 24 * 60 * 60 * 1000

    const result: TAggTradeGap[] = []
    for (const symbol of SymbolList) {
        const gapCheckQuery = `
        SELECT '${symbol}' AS symbol, aggId, time, nextAggId
        FROM (
            SELECT aggId, time, 
                LEAD(aggId) OVER (ORDER BY aggId) AS nextAggId
            FROM agg_trade_${symbol}
            WHERE time >= ?
            AND time <= ?
        )
        WHERE nextAggId IS NOT NULL 
        AND nextAggId != aggId + 1
        LIMIT 10;
    `;
        const gaps = db.query(gapCheckQuery).all(from, to) as TAggTradeGap[];
        result.push(...gaps)
    }
    return result
}


export function insertAggTrade(data: any) {
    const insertQuery = `
        INSERT OR IGNORE INTO agg_trade_${data.symbol} (aggId, price, qty, time, isMaker)
        VALUES (?, ?, ?, ?, ?);
    `;
    const result = db.run(insertQuery, [data.aggId, data.price, data.qty, data.time, data.isMaker]);
    return result.lastInsertRowid;
}

export function insertAggTrades(symbol: string, data: any[]) {
    const insertQuery = `
        INSERT OR IGNORE INTO agg_trade_${symbol} (aggId, price, qty, time, isMaker)
        VALUES (?, ?, ?, ?, ?);
    `;
    const stmt = db.prepare(insertQuery);
    let inserted = 0
    db.transaction(() => {
        for (const row of data) {
            const result = stmt.run(row.aggId, row.price, row.qty, row.time, row.isMaker);
            if (result.lastInsertRowid) {
                inserted++
            }
        }
    })()

    return { inserted };
}

export function deleteOldAggTrades(symbol: string, ttl = 30 * 60 * 1000) {
    const deleteQuery = `
       DELETE FROM agg_trade_${symbol}
       WHERE time < ?;
    `;
    const result = db.run(deleteQuery, [Date.now() - ttl]);
    return { deletedCount: result.changes };
}

export function findAggTrades(params: {
    symbol: string;
    startTime?: number;
    endTime?: number;
    limit?: number;
}) {
    const whereClauses: string[] = [];
    const values: any[] = [];

    if (params.startTime) {
        whereClauses.push('time >= ?');
        values.push(params.startTime);
    }
    if (params.endTime) {
        whereClauses.push('time <= ?');
        values.push(params.endTime);
    }

    const whereClause = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
    const orderDirection = params.startTime && !params.endTime ? 'ASC' : 'DESC';
    const limitClause = params.limit || (!params.startTime && !params.endTime) ? `LIMIT ${params.limit || 200}` : '';

    const query = `
        SELECT * FROM agg_trade_${params.symbol}
        ${whereClause}
        ORDER BY time ${orderDirection}
        ${limitClause}
    `;
    const aggTrades = db.query(query).all(...values) as TAggTrade[];

    if (!params.startTime || params.endTime) {
        aggTrades.reverse(); // Reverse only if startTime is not provided
    }

    return aggTrades;
}

export function findAggTradeTimeRange(symbol: string) {
    const query = `
        SELECT MIN(time) AS minTime, MAX(time) AS maxTime
        FROM agg_trade_${symbol}
    `;
    const result = db.query(query).get() as { minTime: number, maxTime: number };
    return result;
}

export function getTradeDist(symbol: string, timeframe: string, startTime: number, endTime: number) {
    const getPriceRangeQuery = db.prepare(`
        SELECT MIN(price) AS minPrice, MAX(price) AS maxPrice
        FROM agg_trade_${symbol}
        WHERE time >= ? AND time < ?
    `);

    const getTradeDistQuery = db.prepare(`
        SELECT
        CASE
            WHEN CAST(FLOOR((price - ?)/ ?) AS INTEGER) = ?
            THEN ? - 1
            ELSE CAST(FLOOR((price - ?)/?) AS INTEGER)
        END AS bucketIdx,
        SUM(qty) AS totalQty
        FROM agg_trade_${symbol}
        WHERE time >= ? AND time < ?
        GROUP BY bucketIdx
        ORDER BY bucketIdx DESC
    `);

    const rangeResult = getPriceRangeQuery.get(startTime, endTime) as { minPrice: number | null, maxPrice: number | null };
    if (!rangeResult.minPrice || !rangeResult.maxPrice) return []
    const priceRange = rangeResult.maxPrice - rangeResult.minPrice;

    const timeRange = endTime - startTime
    const binNum = Math.min(Math.max(Math.ceil(timeRange / TimeframeMillsMap[timeframe] * 2), 4), 40)
    const binSize = priceRange / binNum;
    const tradeDistResult = getTradeDistQuery.all(rangeResult.minPrice, binSize, binNum, binNum, rangeResult.minPrice, binSize, startTime, endTime,) as { bucketIdx: number, totalQty: number }[];
    return tradeDistResult;
}
