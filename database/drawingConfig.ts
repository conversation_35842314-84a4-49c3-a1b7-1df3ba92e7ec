import { db } from './sqlite';

export const findDrawingConfigs = (symbol?: string, chart?: string) => {
    let query = 'SELECT * FROM drawing_config';
    const conditions: string[] = [];
    const values: any[] = [];

    if (symbol) {
        conditions.push('symbol = ?');
        values.push(symbol);
    }

    if (chart) {
        conditions.push('chart = ?');
        values.push(chart);
    }

    if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY createdAt ASC';

    const rows = db.query(query).all(...values) as any[];

    return rows.map(row => ({
        id: row.id,
        symbol: row.symbol,
        timeframe: row.timeframe,
        chart: row.chart,
        type: row.type,
        dataPoints: JSON.parse(row.dataPoints),
        createdAt: row.createdAt
    }));
};

export const findDrawingConfigById = (id: number) => {
    const query = 'SELECT * FROM drawing_config WHERE id = ? LIMIT 1';
    const row = db.query(query).get(id) as any;

    if (!row) return null;

    return {
        id: row.id,
        symbol: row.symbol,
        timeframe: row.timeframe,
        chart: row.chart,
        type: row.type,
        dataPoints: JSON.parse(row.dataPoints),
        createdAt: row.createdAt
    };
};

export const createDrawingConfig = (data: {
    symbol: string;
    timeframe: string;
    chart: string;
    type: string;
    dataPoints: { time: number; value: number }[];
}) => {
    const createdAt = Date.now();

    const query = `
        INSERT INTO drawing_config (
            symbol, timeframe, chart, type, dataPoints, createdAt
        ) VALUES (?, ?, ?, ?, ?, ?)
    `;

    const result = db.run(query, [
        data.symbol,
        data.timeframe,
        data.chart,
        data.type,
        JSON.stringify(data.dataPoints),
        createdAt
    ]);
    const id = result.lastInsertRowid;

    return {
        id: id as number,
        symbol: data.symbol,
        timeframe: data.timeframe,
        chart: data.chart,
        type: data.type,
        dataPoints: data.dataPoints,
        createdAt
    }
};

export const updateDrawingConfig = (
    id: number,
    dataPoints: { time: number; value: number }[]
) => {
    const query = 'UPDATE drawing_config SET dataPoints = ? WHERE id = ?';
    const result = db.run(query, [JSON.stringify(dataPoints), id]);

    return { modifiedCount: result.changes };
};

export const deleteDrawingConfig = (id: number) => {
    const query = 'DELETE FROM drawing_config WHERE id = ?';
    const result = db.run(query, [id]);

    return { deletedCount: result.changes };
};

export const deleteDrawingConfigsBySymbol = (symbol: string) => {
    const query = 'DELETE FROM drawing_config WHERE symbol = ? RETURNING rowid';
    const result = db.query(query).all(symbol);
    return result;
};

