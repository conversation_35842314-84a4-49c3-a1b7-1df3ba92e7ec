import { db } from "./sqlite";
import { TNewOrderTrigger, TOrderTrigger, EOrderTriggerStatus, EOrderTriggerType, EOrderSide } from "../types/tradeOrder.types";

export function createOrderTrigger(newTrigger: TNewOrderTrigger): TOrderTrigger {
    const createdAt = Date.now();
    const query = `
        INSERT INTO order_trigger (
            symbol, triggerType, triggerPrice, orderSide, usdAmount, status, createdAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    const result = db.run(query, [
        newTrigger.symbol,
        newTrigger.triggerType,
        newTrigger.triggerPrice,
        newTrigger.orderSide || null,
        newTrigger.usdAmount || null,
        EOrderTriggerStatus.ACTIVE,
        createdAt
    ]);

    const id = result.lastInsertRowid as number;

    return {
        id,
        symbol: newTrigger.symbol,
        triggerType: newTrigger.triggerType,
        triggerPrice: newTrigger.triggerPrice,
        orderSide: newTrigger.orderSide,
        usdAmount: newTrigger.usdAmount,
        status: EOrderTriggerStatus.ACTIVE,
        createdAt
    };
}

export function findOrderTriggers(params: {
    symbol?: string;
    status?: EOrderTriggerStatus;
    triggerType?: EOrderTriggerType;
    limit?: number;
}): TOrderTrigger[] {
    let query = 'SELECT * FROM order_trigger';
    const conditions: string[] = [];
    const values: any[] = [];

    if (params.symbol) {
        conditions.push('symbol = ?');
        values.push(params.symbol);
    }

    if (params.status) {
        conditions.push('status = ?');
        values.push(params.status);
    }

    if (params.triggerType) {
        conditions.push('triggerType = ?');
        values.push(params.triggerType);
    }

    if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY createdAt DESC';

    if (params.limit) {
        query += ` LIMIT ${params.limit}`;
    }

    const rows = db.query(query).all(...values) as any[];

    return rows.map(row => ({
        id: row.id,
        symbol: row.symbol,
        triggerType: row.triggerType as EOrderTriggerType,
        triggerPrice: row.triggerPrice,
        orderSide: row.orderSide ? row.orderSide as EOrderSide : undefined,
        usdAmount: row.usdAmount || undefined,
        status: row.status as EOrderTriggerStatus,
        createdAt: row.createdAt,
        triggeredAt: row.triggeredAt || undefined,
        orderId: row.orderId || undefined
    }));
}

export function findOrderTriggerById(id: number): TOrderTrigger | null {
    const query = 'SELECT * FROM order_trigger WHERE id = ? LIMIT 1';
    const row = db.query(query).get(id) as any;

    if (!row) return null;

    return {
        id: row.id,
        symbol: row.symbol,
        triggerType: row.triggerType as EOrderTriggerType,
        triggerPrice: row.triggerPrice,
        orderSide: row.orderSide ? row.orderSide as EOrderSide : undefined,
        usdAmount: row.usdAmount || undefined,
        status: row.status as EOrderTriggerStatus,
        createdAt: row.createdAt,
        triggeredAt: row.triggeredAt || undefined,
        orderId: row.orderId || undefined
    };
}

export function updateOrderTriggerStatus(
    id: number,
    status: EOrderTriggerStatus,
    orderId?: string
): boolean {
    const updates: string[] = ['status = ?'];
    const values: any[] = [status];

    if (status === EOrderTriggerStatus.TRIGGERED) {
        updates.push('triggeredAt = ?');
        values.push(Date.now());
    }

    if (orderId) {
        updates.push('orderId = ?');
        values.push(orderId);
    }

    values.push(id);

    const query = `UPDATE order_trigger SET ${updates.join(', ')} WHERE id = ?`;
    const result = db.run(query, values);

    return result.changes > 0;
}

// Atomic update that only succeeds if trigger is still active
export function updateOrderTriggerStatusIfActive(
    id: number,
    newStatus: EOrderTriggerStatus,
    orderId?: string
): boolean {
    const updates: string[] = ['status = ?'];
    const values: any[] = [newStatus];

    if (newStatus === EOrderTriggerStatus.TRIGGERED) {
        updates.push('triggeredAt = ?');
        values.push(Date.now());
    }

    if (orderId) {
        updates.push('orderId = ?');
        values.push(orderId);
    }

    values.push(id);
    values.push(EOrderTriggerStatus.ACTIVE);

    // Only update if current status is ACTIVE
    const query = `UPDATE order_trigger SET ${updates.join(', ')} WHERE id = ? AND status = ?`;
    const result = db.run(query, values);

    return result.changes > 0;
}

export function deleteOrderTrigger(id: number): boolean {
    const query = 'DELETE FROM order_trigger WHERE id = ?';
    const result = db.run(query, [id]);

    return result.changes > 0;
}

export function findActiveTriggersBySymbol(symbol: string): TOrderTrigger[] {
    return findOrderTriggers({
        symbol,
        status: EOrderTriggerStatus.ACTIVE
    });
}

export function findTriggersForPriceCheck(symbol: string, currentPrice: number): TOrderTrigger[] {
    // Use a transaction to ensure we get a consistent view and can update atomically
    const query = `
        SELECT * FROM order_trigger
        WHERE symbol = ?
        AND status = ?
        AND (
            (triggerType IN (?, ?) AND triggerPrice <= ?) OR
            (triggerType IN (?, ?) AND triggerPrice >= ?)
        )
        ORDER BY createdAt ASC
    `;

    const rows = db.query(query).all(
        symbol,
        EOrderTriggerStatus.ACTIVE,
        EOrderTriggerType.STOP_LOSS,
        EOrderTriggerType.LIMIT_BUY,
        currentPrice,
        EOrderTriggerType.TAKE_PROFIT,
        EOrderTriggerType.LIMIT_SELL,
        currentPrice
    ) as any[];

    return rows.map(row => ({
        id: row.id,
        symbol: row.symbol,
        triggerType: row.triggerType as EOrderTriggerType,
        triggerPrice: row.triggerPrice,
        orderSide: row.orderSide ? row.orderSide as EOrderSide : undefined,
        usdAmount: row.usdAmount || undefined,
        status: row.status as EOrderTriggerStatus,
        createdAt: row.createdAt,
        triggeredAt: row.triggeredAt || undefined,
        orderId: row.orderId || undefined
    }));
}
