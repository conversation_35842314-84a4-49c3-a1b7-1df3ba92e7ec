import { retry } from "radash";
import { db } from "./sqlite";
import { TOhlcv, TNewOhlcv } from "../types/ohlcv.types";

// 查询 OHLCV 数据
export async function findOhlcvs(params: {
    symbol: string;
    timeframe: string;
    startTime?: number;
    endTime?: number;
    limit?: number;
}): Promise<TOhlcv[]> {
    const tableName = `ohlcv_${params.symbol}_${params.timeframe}`;
    const whereClauses: string[] = [];
    const values: any[] = [];

    if (params.startTime) {
        whereClauses.push('time >= ?');
        values.push(params.startTime);
    }
    if (params.endTime) {
        whereClauses.push('time <= ?');
        values.push(params.endTime);
    }

    const whereClause = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
    const orderDirection = params.startTime && !params.endTime ? 'ASC' : 'DESC';
    const limitClause = params.limit || (!params.startTime && !params.endTime) ? `LIMIT ${params.limit || 200}` : '';

    const query = `
        SELECT * FROM ${tableName}
        ${whereClause}
        ORDER BY time ${orderDirection}
        ${limitClause}
    `;
    const ohlcvList = await retry({ times: 2, delay: 1000 }, async () => {
        return db.query(query).all(...values) as TOhlcv[]
    })

    if (!params.startTime || params.endTime) {
        ohlcvList.reverse(); // Reverse only if startTime is not provided
    }

    return ohlcvList;
}

export function findLatestOhlcv(symbol: string, timeframe: string) {
    const tableName = `ohlcv_${symbol}_${timeframe}`;
    const query = `
        SELECT * FROM ${tableName}
        ORDER BY time DESC
        LIMIT 1
    `;
    const ohlcv = db.query(query).get() as TOhlcv
    return ohlcv
}

// 插入或更新单个 OHLCV 数据
export function upsertOhlcv(symbol: string, timeframe: string, data: TNewOhlcv) {
    const tableName = `ohlcv_${symbol}_${timeframe}`;

    // 尝试更新，若不存在则插入
    const upsertQuery = `
        INSERT INTO ${tableName} (time, open, high, low, close, volume)
        VALUES (?, ?, ?, ?, ?, ?)
        ON CONFLICT(time) DO UPDATE SET
        open = excluded.open,
        high = excluded.high,
        low = excluded.low,
        close = excluded.close,
        volume = excluded.volume;
    `;

    return db.run(upsertQuery, [data.time, data.open, data.high, data.low, data.close, data.volume]);
}

// 批量插入 OHLCV 数据，跳过重复项
export function insertOhlcvs(symbol: string, timeframe: string, data: TNewOhlcv[]) {
    const tableName = `ohlcv_${symbol}_${timeframe}`;

    const insertQuery = `
        INSERT OR IGNORE INTO ${tableName} (time, open, high, low, close, volume)
        VALUES (?, ?, ?, ?, ?, ?);
    `;

    const stmt = db.prepare(insertQuery);

    let inserted = 0
    db.transaction(() => {
        for (const row of data) {
            const result = stmt.run(row.time, row.open, row.high, row.low, row.close, row.volume);
            if (result.lastInsertRowid) {
                inserted++
            }
        }
    })()

    return { inserted };
}

export function cleanOhlcvList(symbol: string, timeframe: string) {
    const tableName = `ohlcv_${symbol}_${timeframe}`;

    const deleteQuery = `
        DELETE FROM ${tableName} 
    `;

    return db.run(deleteQuery)

}