import { db } from './sqlite';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>rg, TAlertOp } from '../types/alertConfig.types';

export const findAlertConfigs = (params?: {
    symbol?: string;
    timeframe?: string;
    enabled?: boolean;
    includesGeneral?: boolean;
}) => {
    let query = 'SELECT * FROM alert_config';
    const conditions: string[] = [];
    const values: any[] = [];

    if (params?.symbol) {
        conditions.push('symbol = ?');
        values.push(params.symbol);
    }

    if (params?.timeframe) {
        conditions.push('timeframe = ?');
        values.push(params.timeframe);
    }

    if (params?.enabled !== undefined) {
        conditions.push('enabled = ?');
        values.push(params.enabled ? 1 : 0);
    }

    if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
    }

    // Handle includesGeneral separately
    if (params?.includesGeneral) {
        if (conditions.length > 0) {
            // If we already have conditions, use OR to include general alerts
            query = `SELECT * FROM alert_config WHERE (${conditions.join(' AND ')}) OR (general = 1`;
            if (params?.enabled !== undefined) {
                query += ` AND enabled = ${params.enabled ? 1 : 0}`;
            }
            query += ')';
        } else {
            // If no conditions, just add general = 1
            query += ' WHERE general = 1';
            if (params?.enabled !== undefined) {
                query += ` AND enabled = ${params.enabled ? 1 : 0}`;
            }
        }
    }

    query += ' ORDER BY createdAt DESC';

    const rows = db.query(query).all(...values) as any[];

    return rows.map(row => ({
        id: row.id,
        symbol: row.symbol,
        timeframe: row.timeframe,
        arg1: JSON.parse(row.arg1),
        arg2: row.arg2 ? JSON.parse(row.arg2) : undefined,
        enabled: Boolean(row.enabled),
        triggerType: row.triggerType,
        op: JSON.parse(row.op),
        muted: Boolean(row.muted),
        general: Boolean(row.general),
        chart: row.chart,
        createdAt: row.createdAt
    }));
};

export const findAlertConfigById = (id: number) => {
    const query = 'SELECT * FROM alert_config WHERE id = ? LIMIT 1';
    const row = db.query(query).get(id) as any;

    if (!row) return null;

    return {
        id: row.id,
        symbol: row.symbol,
        timeframe: row.timeframe,
        arg1: JSON.parse(row.arg1),
        arg2: row.arg2 ? JSON.parse(row.arg2) : undefined,
        enabled: Boolean(row.enabled),
        triggerType: row.triggerType,
        op: JSON.parse(row.op),
        muted: Boolean(row.muted),
        general: Boolean(row.general),
        chart: row.chart,
        createdAt: row.createdAt
    };
};

export const deleteAlertConfigById = (id: number) => {
    const query = 'DELETE FROM alert_config WHERE id = ?';
    db.run(query, [id]);
};

export const updateAlertConfigById = (
    id: number,
    updates: {
        arg1?: TAlertArg;
        arg2?: TAlertArg;
        op?: TAlertOp;
        enabled?: boolean;
        triggerType?: string;
        muted?: boolean;
        chart?: string;
    },
) => {
    const alertConfig = findAlertConfigById(id);
    if (!alertConfig) {
        return { acknowledged: false };
    }

    const setStatements: string[] = [];
    const values: any[] = [];

    if (updates.arg1) {
        setStatements.push('arg1 = ?');
        values.push(JSON.stringify(updates.arg1));
    }

    if (updates.arg2 !== undefined) {
        setStatements.push('arg2 = ?');
        values.push(updates.arg2 ? JSON.stringify(updates.arg2) : null);
    }

    if (updates.op) {
        setStatements.push('op = ?');
        values.push(JSON.stringify(updates.op));
    }

    if (updates.enabled !== undefined) {
        setStatements.push('enabled = ?');
        values.push(updates.enabled ? 1 : 0);
    }

    if (updates.triggerType) {
        setStatements.push('triggerType = ?');
        values.push(updates.triggerType);
    }

    if (updates.muted !== undefined) {
        setStatements.push('muted = ?');
        values.push(updates.muted ? 1 : 0);
    }

    if (updates.chart !== undefined) {
        setStatements.push('chart = ?');
        values.push(updates.chart);
    }

    if (setStatements.length === 0) {
        return { acknowledged: true }; // Nothing to update
    }

    const query = `UPDATE alert_config SET ${setStatements.join(', ')} WHERE id = ?`;
    values.push(id);

    const result = db.run(query, values);
    return { acknowledged: result.changes > 0 };
};

export const createAlertConfig = (doc: {
    symbol?: string;
    timeframe?: string;
    arg1: TAlertArg;
    arg2?: TAlertArg;
    enabled: boolean;
    triggerType: string;
    op: TAlertOp;
    muted: boolean;
    general: boolean;
    chart?: string;
}) => {
    const createdAt = Date.now();

    // If general is true, remove symbol and timeframe
    if (doc.general) {
        doc.symbol = undefined;
        doc.timeframe = undefined;
    }

    const query = `
        INSERT INTO alert_config (
            symbol, timeframe, arg1, arg2, enabled, triggerType, op, muted, general, chart, createdAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING *
    `;

    const id = db.run(query, [
        doc.symbol ?? '',
        doc.timeframe ?? '',
        JSON.stringify(doc.arg1),
        doc.arg2 ? JSON.stringify(doc.arg2) : '',
        doc.enabled ? 1 : 0,
        doc.triggerType,
        JSON.stringify(doc.op),
        doc.muted ? 1 : 0,
        doc.general ? 1 : 0,
        doc.chart ?? '',
        createdAt
    ]).lastInsertRowid


    return {
        id: id as number,
        symbol: doc.symbol,
        timeframe: doc.timeframe,
        arg1: doc.arg1,
        arg2: doc.arg2,
        enabled: doc.enabled,
        triggerType: doc.triggerType,
        op: doc.op,
        muted: doc.muted,
        general: doc.general,
        chart: doc.chart,
        createdAt
    };
};

export const findAlertConfigByDrawingConfigId = (drawingConfigId: number) => {
    const query = `
        SELECT * FROM alert_config
        WHERE json_extract(arg1, '$.refId') = ?
        OR json_extract(arg2, '$.refId') = ?
    `;

    const rows = db.query(query).all(drawingConfigId, drawingConfigId) as any[];

    return rows.map(row => ({
        id: row.id,
        symbol: row.symbol,
        timeframe: row.timeframe,
        arg1: JSON.parse(row.arg1),
        arg2: row.arg2 ? JSON.parse(row.arg2) : undefined,
        enabled: Boolean(row.enabled),
        triggerType: row.triggerType,
        op: JSON.parse(row.op),
        muted: Boolean(row.muted),
        general: Boolean(row.general),
        chart: row.chart,
        createdAt: row.createdAt
    }));
};
