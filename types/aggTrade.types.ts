export interface TAggTradeGap {
    symbol: string;
    aggId: number;
    time: number;
    nextAggId: number;
}

export interface TAggTrade {
    id: number;
    aggId: number;
    price: number;
    qty: number;
    time: number;
    isMaker: number;
}

export interface TAnalyzeAggTrade {
    syncId: number;
    aggId: number;
    price: number;
    qty: number;
    time: number;
    isMaker: number;
}