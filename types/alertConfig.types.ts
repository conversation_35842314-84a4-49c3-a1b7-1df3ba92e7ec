export interface TAlertArg {
    type: string;
    value?: number;
    name: string;
    refId: string;
    scriptName?: string;
}

export interface TAlertOp {
    name: string;
    type: string;
}

export interface TAlertConfig {
    id: number;
    symbol?: string;
    timeframe?: string;
    arg1: TAlertArg;
    arg2?: TAlertArg;
    enabled: boolean;
    triggerType: string;
    op: TAlertOp;
    muted: boolean;
    general: boolean;
    chart?: string;
    createdAt: number;
}

export enum EAlertTrigger {
    Once = 'once',
    OncePerCandle = 'oncePerCandle',
    BeforeClosed = 'beforeClosed'
}

export enum EAlertOp {
    Cross = 'cross',
    CrossUp = 'crossUp',
    CrossDown = 'crossDown',
    CrossChannel = 'crossChannel',
    ExitChannel = 'exitChannel',
    EnterChannel = 'enterChannel'
}


export type TNullableNumber = number | undefined | null