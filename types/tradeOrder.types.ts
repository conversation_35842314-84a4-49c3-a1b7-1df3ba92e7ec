
export enum EPositionSide {
    LONG = 'long',
    SHORT = 'short'
}

export enum EOrderSide {
    BUY = 'buy',
    SELL = 'sell'
}


export interface TTradeOrder {
    id: number;
    symbol: string;
    orderId: string;
    side: EOrderSide;
    isClose: number;
    createdAt: number;
}

export interface TNewTradeOrder {
    symbol: string;
    orderId: string;
    side: EOrderSide;
    isClose?: number;
}


export interface TOpenPosition {
    symbol: string;
    side: EPositionSide;
}

export enum EOrderTriggerType {
    STOP_LOSS = 'stop_loss',
    TAKE_PROFIT = 'take_profit',
    LIMIT_BUY = 'limit_buy',
    LIMIT_SELL = 'limit_sell'
}

export enum EOrderTriggerStatus {
    ACTIVE = 'active',
    TRIGGERED = 'triggered',
    CANCELLED = 'cancelled'
}

export interface TOrderTrigger {
    id: number;
    symbol: string;
    triggerType: EOrderTriggerType;
    triggerPrice: number;
    orderSide?: EOrderSide; // Optional for STOP_LOSS and TAKE_PROFIT
    usdAmount?: number; // Optional for STOP_LOSS and TAKE_PROFIT
    status: EOrderTriggerStatus;
    createdAt: number;
    triggeredAt?: number;
    orderId?: string;
}

export interface TNewOrderTrigger {
    symbol: string;
    triggerType: EOrderTriggerType;
    triggerPrice: number;
    orderSide?: EOrderSide; // Optional for STOP_LOSS and TAKE_PROFIT
    usdAmount?: number; // Optional for STOP_LOSS and TAKE_PROFIT
}

