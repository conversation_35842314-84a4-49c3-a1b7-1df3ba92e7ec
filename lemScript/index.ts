import { <PERSON>riptRunner, TAlertData, TOhlcv, TPlotData, TScript, TScriptOutput } from 'lem-script';
import fs from 'fs';
import cacheStore from '../common/cache';
export const scriptRunner = new ScriptRunner()


let scriptList: TScript[] = []

const loadScripts = async () => {
    console.info('loading lem scripts')
    scriptList = []

    const { scriptConfigList } = (await import(`./config.ts?${Date.now()}`));
    for (let i = 0; i < scriptConfigList.length; i++) {
        const scriptConfig = scriptConfigList[i]
        const { default: fn } = (await import(`./scripts/${scriptConfig.script}?${Date.now()}`))
        const id = fn.name + i.toString()
        scriptList.push({
            id,
            name: scriptConfig.script,
            fn,
            inputs: scriptConfig.inputs,
        })
    }
    cacheStore.deleteTaggedItems(`script:vars`)
    cacheStore.deleteTaggedItems(`script-alert`)

}
await loadScripts()

fs.watch(__dirname + '/config.ts', async (eventType) => {
    if (eventType === 'change') {
        try {
            await loadScripts()
        } catch (error) {
        }
    }
});

fs.watch(__dirname + '/scripts', async (eventType, filename) => {
    if (filename && eventType === 'change') {
        try {
            await loadScripts()
        } catch (error) {
        }
    }
});

export function getScriptList() {
    return scriptList
}

export function getScriptOutputList(ohlcvList: TOhlcv[], options?: { outputLen?: number }) {
    const startTime = ohlcvList[0].time
    const endTime = ohlcvList[ohlcvList.length - 1].time
    const outputList: TScriptOutput[] = []

    for (const script of scriptList) {
        const vars = cacheStore.get(`script:${script.id}:vars:${startTime}:${endTime}`)

        const ctx = scriptRunner.run({
            script,
            ohlcvList,
            vars
        })
        outputList.push(ctx.getOutput(options?.outputLen))
    }
    return outputList
}

export function getPlotList(symbol: string, timeframe: string, ohlcvList: TOhlcv[], options?: { outputLen?: number, saveVars?: boolean }) {
    const startTime = ohlcvList[0].time
    const endTime = ohlcvList[ohlcvList.length - 1].time
    const outputList: TPlotData[] = []

    for (const script of scriptList) {
        const key = `script:${script.id}:vars:${symbol}:${timeframe}:${startTime}:${endTime}`
        const vars = cacheStore.get(key)
        const ctx = scriptRunner.run({
            script,
            ohlcvList,
            vars
        })

        if (!vars && options?.saveVars) {
            const tag = `script:${script.id}:vars:${symbol}:${timeframe}`
            cacheStore.deleteTaggedItems(tag)
            cacheStore.set(key, ctx.getVars())
            cacheStore.addTag(key, tag)
            cacheStore.addTag(key, `script:vars`)
        }
        outputList.push(...ctx.getPlots(options?.outputLen))
    }
    return outputList
}

export function getScriptAlertList(symbol: string, timeframe: string, ohlcvList: TOhlcv[], options?: { saveVars?: boolean }) {
    const startTime = ohlcvList[0].time
    const endTime = ohlcvList[ohlcvList.length - 1].time
    const alertList: TAlertData[] = []

    for (const script of scriptList) {
        const key = `script:${script.id}:vars:${symbol}:${timeframe}:${startTime}:${endTime}`
        const vars = cacheStore.get(key)
        const ctx = scriptRunner.run({
            script,
            ohlcvList,
            vars
        })

        if (!vars && options?.saveVars) {
            const tag = `script:${script.id}:vars:${symbol}:${timeframe}`
            cacheStore.deleteTaggedItems(tag)
            cacheStore.set(key, ctx.getVars())
            cacheStore.addTag(key, tag)
            cacheStore.addTag(key, `script:vars`)
        }
        alertList.push(...ctx.getAlerts())
    }
    return alertList
}

