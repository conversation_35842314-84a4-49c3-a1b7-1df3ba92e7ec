import { EPlot, type ScriptContext } from "lem-script"

const defaultInputs = {
    fastLen: 12,
    slowLen: 26,
    signalLen: 9
}

export default function macd(this: ScriptContext, params: typeof defaultInputs = defaultInputs) {
    this.overlay = false

    const src = this.close

    // Calculate fast and slow EMAs
    const fastEma = this.f.ema(src, params.fastLen)
    const slowEma = this.f.ema(src, params.slowLen)

    // Calculate MACD line (difference between fast and slow EMA)
    const macdLine = this.num(fastEma.v - slowEma.v)

    // Calculate signal line (EMA of MACD line)
    const signalLine = this.f.ema(macdLine, params.signalLen)

    // Calculate histogram (difference between MACD line and signal line)
    const histogram = this.num(macdLine.v - signalLine.v)

    // Plot MACD line
    this.plot({
        series: macdLine,
        color: '#A0C878',
        name: 'MACD'
    })

    // Plot signal line
    this.plot({
        series: signalLine,
        color: '#DC8BE0',
        name: 'Signal'
    })

    // Plot histogram
    this.plot({
        series: histogram,
        color: histogram.v >= 0 ? '#A0C878' : '#DC8BE0',
        name: 'Histogram',
        type: EPlot.Histogram
    })

    // // Plot zero line for reference
    // const zeroLine = this.num(0)
    // this.plot({
    //     series: zeroLine,
    //     color: '#666666',
    //     name: 'Zero Line'
    // })
}
