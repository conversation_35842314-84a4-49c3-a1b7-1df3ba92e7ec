import { type ScriptContext } from "lem-script"

const defaultInputs = { len: 100 }

export default function hma(this: ScriptContext, params: typeof defaultInputs = defaultInputs) {
    const src = this.close
    const diff = this.num(2 * this.f.wma(src, params.len / 2).v - this.f.wma(src, params.len).v)
    const hullma = this.f.wma(diff, Math.floor(Math.sqrt(params.len)))
    this.plot({ series: hullma, color: '#73EC8B', name: 'hma' })
}
