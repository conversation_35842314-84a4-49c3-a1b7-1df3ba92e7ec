import { type ScriptContext } from "lem-script"

const defaultInputs = { len1: 5, len2: 9, len3: 13, len4: 60 }

// multiple emas
export default function me(this: ScriptContext, params: typeof defaultInputs = defaultInputs) {
    const src = this.close
    // const ema1 = this.f.ema(src, params.len1)
    // const ema2 = this.f.ema(src, params.len2)
    // const ema3 = this.f.ema(src, params.len3)
    const ema4 = this.f.ema(src, params.len4)
    // this.plot({ series: ema1, color: '#FFE100', name: `ema ${params.len1}` })
    // this.plot({ series: ema2, color: '#FFE100', name: `ema ${params.len2}` })
    // this.plot({ series: ema3, color: '#FFE100', name: `ema ${params.len3}` })
    this.plot({ series: ema4, color: '#FFE100', name: `ema ${params.len4}` })
}
