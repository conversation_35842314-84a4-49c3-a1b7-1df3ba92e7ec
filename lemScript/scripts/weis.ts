import { EPlot, type ScriptContext } from "lem-script"

const defaultInputs = {
    methodType: 'ATR' as 'ATR' | 'Traditional' | 'Part of Price',
    methodValue: 14,
    priceSource: 'Close' as 'Close' | 'Open / Close' | 'High / Low',
    useTrueRange: 'Auto' as 'Always' | 'Auto' | 'Never',
    normalize: false
}

const colorUp = '#40ff40'
const colorDown = '#FF9587'

export default function weis(this: ScriptContext, params: typeof defaultInputs = defaultInputs) {
    this.overlay = false

    // Initialize variables
    const currclose = this.num(NaN)
    const direction = this.num(NaN)
    const barcount = this.num(1)
    const vol = this.num(0)
    const res = this.num(0)

    // Determine volume source
    const useClose = params.priceSource === 'Close'
    const useOpenClose = params.priceSource === 'Open / Close' || useClose
    const shouldUseTrueRange = params.useTrueRange === 'Always' ||
        (params.useTrueRange === 'Auto' && (this.volume.v === 0 || isNaN(this.volume.v)))

    // Calculate true range manually
    const trueRange = Math.max(
        this.high.v - this.low.v,
        Math.abs(this.high.v - this.f.nz(this.close.back(1)).v),
        Math.abs(this.low.v - this.f.nz(this.close.back(1)).v)
    )
    vol.v = shouldUseTrueRange ? trueRange : this.volume.v

    // Determine price values
    const op = useClose ? this.close.v : this.open.v
    const hi = useOpenClose ?
        (this.close.v >= op ? this.close.v : op) :
        this.high.v
    const lo = useOpenClose ?
        (this.close.v <= op ? this.close.v : op) :
        this.low.v

    // Calculate method value
    let methodValue = params.methodValue
    if (params.methodType === 'ATR') {
        methodValue = this.f.atr(Math.round(params.methodValue)).v
    } else if (params.methodType === 'Part of Price') {
        methodValue = this.close.v / params.methodValue
    }

    // Calculate current close based on Renko logic
    const prevclose = this.f.nz(currclose.back(1)).v
    const prevhigh = prevclose + methodValue
    const prevlow = prevclose - methodValue

    currclose.v = hi > prevhigh ? hi : lo < prevlow ? lo : prevclose

    // Determine direction
    const prevDirection = this.f.nz(direction.back(1)).v
    direction.v = currclose.v > prevclose ? 1 :
        currclose.v < prevclose ? -1 :
            prevDirection

    // Check if direction has changed
    const directionHasChanged = direction.v !== this.f.nz(direction.back(1)).v
    const directionIsUp = direction.v > 0
    const directionIsDown = direction.v < 0

    // Calculate bar count and volume
    if (!directionHasChanged && params.normalize) {
        barcount.v = this.f.nz(barcount.back(1)).v + 1
    } else {
        barcount.v = 1
    }

    if (!directionHasChanged) {
        vol.v = this.f.nz(vol.back(1)).v + vol.v
    }

    // Calculate result
    res.v = barcount.v > 1 ? vol.v / barcount.v : vol.v

    // Apply oscillating if enabled

    const color = this.str(directionIsDown ? colorDown : colorUp)

    // Plot the result
    this.plot({
        type: EPlot.Histogram,
        series: res,
        name: 'Wave Volume',
        color: color.v
    })


    // if (this.isLast) {
    //     if (color.back(1).v !== color.v) {
    //         const maxVols: number[] = []
    //         const targetColor = color.back(1).v
    //         console.log(targetColor)
    //         maxVols.push(res.back(1).v)
    //         let i = 2
    //         while (maxVols.length < 3) {
    //             if (color.back(i).v === targetColor && color.back(i - 1).v !== targetColor) {
    //                 maxVols.push(res.back(i).v)
    //             }
    //             i++
    //         }
    //         if (maxVols[0] > maxVols[1] && maxVols[1] > maxVols[2]) {
    //             this.alert('weis ascending')
    //         } else if (maxVols[0] < maxVols[1] && maxVols[1] < maxVols[2]) {
    //             this.alert('weis descending')
    //         }
    //     }
    // }
}
