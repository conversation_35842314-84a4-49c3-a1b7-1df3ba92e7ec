import { type ScriptContext } from "lem-script"

const defaultInputs = { len: 21 }

export default function kama(this: ScriptContext, params: typeof defaultInputs = defaultInputs) {
    const xPrice = this.close

    const xvnoise = this.f.abs(this.f.diff(xPrice))
    const nAMA = this.num(0)
    const nfastend = 0.666
    const nslowend = 0.0645
    const nsignal = this.f.abs(this.f.diff(xPrice, params.len))
    const nnoise = this.f.sum(xvnoise, params.len)
    const nefratio = nnoise.v != 0 ? nsignal.v / nnoise.v : 0
    const nsmooth = Math.pow(nefratio * (nfastend - nslowend) + nslowend, 2)
    nAMA.v = this.f.nz(nAMA.back(1)).v + nsmooth * (xPrice.v - this.f.nz(nAMA.back(1)).v)
    this.plot({ series: nAMA, color: '#56DFCF', name: 'kama' })

    // if (this.isLast) {
    //     this.f.bounceDown(this.close, nAMA, 10).v && this.alert('bounce down on kama')
    //     this.f.bounceUp(this.close, nAMA, 10).v && this.alert('bounce up on kama')
    // }
}
