{"dependencies": {"@duckdb/node-api": "^1.3.1-alpha.22", "@elysiajs/cors": "^1.3.3", "bun-sqlite-key-value": "^1.13.1", "elysia": "^1.3.1", "hono": "^4.8.2", "http-proxy-agent": "^7.0.2", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "logestic": "^1.2.4", "mathjs": "^14.5.1", "ms": "^2.1.3", "qs": "^6.14.0", "radash": "^12.1.0", "web-push": "^3.6.7", "ws": "^8.18.2"}, "devDependencies": {"@types/bun": "^1.2.13", "@types/ws": "^8.18.1", "adm-zip": "^0.5.16", "papaparse": "^5.5.3"}, "scripts": {"pipeline": "bun run app/pipeline.ts", "analytics": "bun run app/analytics.ts", "stream": "bun run app/stream.ts", "api": "bun run app/api.ts", "watch": "bun run app/watch.ts"}}