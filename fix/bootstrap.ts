const symbol = 'hbar'
import { findAggTrades, insertAggTrades } from '../database/aggTrade';
import { fetchAggTrades } from '../exchange/binance';
import { format } from '../pipeline/aggTrade/stages';
import { TPipelineData } from '../types/pipeline.types';

const aggTrades = await findAggTrades({ symbol, limit: 1, endTime: Date.now() })
const latestId = aggTrades[0].aggId

await fetchAggTrades({
    symbol,
    startId: latestId,
    onData: (data) => {
        const list: any[] = []
        for (const d of data) {
            const formated = format({
                type: 'api',
                extra: { symbol },
                data: d
            }) as TPipelineData
            if (formated === null) continue
            list.push(formated.data)
        }
        const result = insertAggTrades(symbol, list)
        console.log(symbol, result.inserted, 'agg trades inserted')
    }
})

