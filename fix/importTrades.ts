const sqlStr = `
CREATE TABLE IF NOT EXISTS ontime  (
    FlightDate DATE,
    UniqueCarrier VARCHAR,
    OriginCityName VARCHAR,
    DestCityName VARCHAR
);
COPY ontime FROM '/Users/<USER>/Projects/yasna/fix/flights.csv';
`



import { DuckDBInstance } from '@duckdb/node-api';
import settings from '../settings';

const instance1 = await DuckDBInstance.create(`/Users/<USER>/Projects/yasna/fix/ontime.db`, {
    access_mode: 'READ_WRITE'
});
const conn1 = await instance1.connect();
await conn1.run(sqlStr);