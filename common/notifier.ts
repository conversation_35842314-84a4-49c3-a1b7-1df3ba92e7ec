import settings from "../settings";
import webPush from 'web-push'
import cache from './cache';


webPush.setVapidDetails(
    'mailto:<EMAIL>',
    settings.notifier.vapid.publicKey,
    settings.notifier.vapid.privateKey
);

export async function sendNotification(title: string, content: string, url?: string) {
    console.info('sending ', title, content)
    const subscriptions = cache.hGetValues('push-subscription')

    for (const key in subscriptions) {
        const subscription = subscriptions[key]
        webPush.sendNotification(subscription, JSON.stringify({
            title,
            message: content,
            url,
        })).catch(e => console.error(e))
    }

    return fetch(settings.notifier.weComPushWebhook, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            "msgtype": "text",
            "text": {
                "content": [title, content].join('\n')
            }
        })
    }).catch(e => console.error(e))
}