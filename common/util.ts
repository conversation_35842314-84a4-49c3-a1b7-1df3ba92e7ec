import settings from '../settings'
export const checkNetwork = async () => {
    try {
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Timeout after 9s')), 9000);
        });

        const fetchPromise = fetch(settings.exchange.binance.apiUrl, { method: 'HEAD' })
            .then((res) => res.ok)
            .catch(() => false);

        const isOnline = await Promise.race([fetchPromise, timeoutPromise]);
        return isOnline;
    } catch (err) {
        console.log(`network check failed: ${err.message}`);
        return false;
    }
};

export const monitorNetwork = async (onOnline?: () => void, onOffline?: () => void) => {
    let prevIsOnline = await checkNetwork()
    return setInterval(async () => {
        const isOnline = await checkNetwork()
        if (!isOnline && prevIsOnline) {
            console.log('network offline')
            onOffline && onOffline()
            prevIsOnline = false
        }
        else if (isOnline && !prevIsOnline) {
            console.log('network online')
            onOnline && onOnline()
            prevIsOnline = true
        }
    }, 10000)
}


export function getFormatNumStr(num: number) {
    let text = "";
    if (Math.abs(num) < 0.4) {
        text = num.toFixed(5);
    } else if (Math.abs(num) < 1) {
        text = num.toFixed(4);
    }
    else {
        text = num.toFixed(3);
    }
    return text
}
