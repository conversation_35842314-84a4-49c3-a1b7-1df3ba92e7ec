import Redis from "ioredis";
import setting from "../settings";

export function createRedisClient(url: string) {
    const client = new RedisClient(url);
    return client;
}


export class RedisClient extends Redis {
    private channelHandlerMap = new Map<string, (msg: string) => void>();
    private reconnectAttempts = 0;
    private maxReconnectAttempts = Number.MAX_SAFE_INTEGER;

    constructor(url: string) {
        super(url, {
            autoResubscribe: true,
            retryStrategy(times) {
                const delay = Math.min(times * 1000, 5000);
                return delay;
            },
            maxRetriesPerRequest: null,
        });

        this.on('connect', () => {
            console.log('redis connected');
            this.reconnectAttempts = 0;
        });

        this.on('ready', () => {
            console.log('redis ready');
            this.resubscribeToChannels();
        });

        this.on('error', (error) => {
            console.error('Redis error: ', error);
        });

        this.on('close', () => {
            console.log('Redis connection closed');
        });

        this.on('reconnecting', (delay: number) => {
            this.reconnectAttempts++;
            console.log(`Redis reconnecting... Attempt ${this.reconnectAttempts} (delay: ${delay}ms)`);

            if (this.reconnectAttempts > this.maxReconnectAttempts) {
                console.error('Max reconnection attempts reached. Stopping reconnection.');
                this.quit();
            }
        });

        this.on('message', (channel, message) => {
            const handler = this.channelHandlerMap.get(channel);
            if (handler) {
                try {
                    handler(message);
                } catch (error) {
                    console.error(`Error handling message for channel ${channel}:`, error);
                }
            }
        });
    }

    sub(channel: string, handler: (msg: string) => void) {
        this.subscribe(channel, (err, count) => {
            if (err) {
                console.error("Failed to subscribe: %s", err.message);
            } else {
                console.log('Subscribed to', channel);
                this.channelHandlerMap.set(channel, handler);
            }
        });
    }

    private resubscribeToChannels() {
        for (const [channel, handler] of this.channelHandlerMap.entries()) {
            this.sub(channel, handler);
        }
    }
}

export const redis = new RedisClient(setting.redis.url);