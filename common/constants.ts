import { EAlertOp, EAlertTrigger } from "../types/alertConfig.types"

export const SymbolList = [
    'sui',
    'xrp',
    'sol',
    'aave',
    'hbar',
]

export const TimeframeList = [
    '5m',
]



export const TRADEING_LEVERAGE = 20

export const TimeframeMillsMap = {
    '1m': 60 * 1000,
    '3m': 3 * 60 * 1000,
    '5m': 5 * 60 * 1000,
    '15m': 15 * 60 * 1000,
    '30m': 30 * 60 * 1000,
    '1h': 60 * 60 * 1000,
    '4h': 4 * 60 * 60 * 1000,
    '6h': 6 * 60 * 60 * 1000,
    '1d': 24 * 60 * 60 * 1000,
    '1w': 7 * 24 * 60 * 60 * 1000,
}

export const AlertTriggerTypeList = [
    {
        name: 'Once',
        type: EAlertTrigger.Once
    },
    {
        name: 'Once per candle',
        type: EAlertTrigger.OncePerCandle
    },
    {
        name: 'Before closed',
        type: EAlertTrigger.BeforeClosed
    },
]

export const AlertOpList = [
    {
        name: 'Cross',
        type: EAlertOp.Cross
    },
    {
        name: 'Cross up',
        type: EAlertOp.CrossUp
    },
    {
        name: 'Cross down',
        type: EAlertOp.CrossDown
    },
    {
        name: 'Cross channel',
        type: EAlertOp.CrossChannel
    },
    {
        name: 'Enter channel',
        type: EAlertOp.EnterChannel
    },
    {
        name: 'Exit channel',
        type: EAlertOp.ExitChannel
    },
]
