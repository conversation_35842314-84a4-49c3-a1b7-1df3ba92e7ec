import type { ServerWebSocket } from "bun";

export class Wss {
    private server: import("bun").Server;
    private topicSubscription: Map<string, number> = new Map()
    private socketList: ServerWebSocket<{ topicList: string[] }>[] = []
    onOpen?: (ws: ServerWebSocket<{ topicList: string[] }>) => void

    constructor(port: number) {
        this.server = Bun.serve<{ topicList: string[] }, {}>({
            port,
            fetch(req, server) {
                const topic = new URL(req.url).searchParams.get('topic') ?? ''
                if (topic) {
                    const success = server.upgrade(req, { data: { topicList: topic.split(',') } });
                    if (success) return undefined;
                }
                return new Response('Missing topic parameter', { status: 400 });

            },
            websocket: {
                idleTimeout: 60 * 10,
                open: (ws) => {
                    for (const topic of ws.data.topicList) {
                        ws.subscribe(topic);
                        this.topicSubscription.set(topic, (this.topicSubscription.get(topic) || 0) + 1)
                        console.log('wss topic subscribed', topic)
                    }
                    this.socketList.push(ws)
                    this.onOpen?.(ws)
                },
                message: (ws, message) => { },
                close: (ws) => {
                    for (const topic of ws.data.topicList) {
                        ws.unsubscribe(topic);
                        this.topicSubscription.set(topic, Math.max(0, (this.topicSubscription.get(topic) || 0) - 1))
                        console.log('wss topic unsubscribed', topic,)
                    }
                    this.socketList = this.socketList.filter(s => s !== ws)
                },
                drain: (ws) => {
                    for (const topic of ws.data.topicList) {
                        ws.unsubscribe(topic);
                        this.topicSubscription.set(topic, Math.max(0, (this.topicSubscription.get(topic) || 0) - 1))
                        console.log('wss topic unsubscribed', topic,)
                    }
                    this.socketList = this.socketList.filter(s => s !== ws)
                },
                ping: (ws) => {
                    ws.pong()
                },
                pong: (ws) => {
                    // console.log('pong');
                }
            },
        });

        setInterval(() => {
            for (const socket of this.socketList) {
                // console.log('ping');
                socket.ping()
            }
        }, 10000)

        console.log('wss', 'listening on port', port)
    }


    pub(topic: string, data: string) {
        if ((this.topicSubscription.get(topic) || 0) > 0) {
            this.server.publish(topic, data)
        }
    }

    getTopicSubscriptionCount(topic: string) {
        return this.topicSubscription.get(topic) || 0
    }
}



