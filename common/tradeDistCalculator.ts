import { TAggTrade } from "../types/aggTrade.types";
import { max, mean, min, std } from "mathjs";

export class TradeDistributionCalculator {
    trades: TAggTrade[]
    bucketNum: number

    constructor(params: { bucketNum?: number, trades: TAggTrade[] }) {
        this.trades = params.trades
        this.bucketNum = params.bucketNum ?? 7

    }

    get distribution() {
        // Find price range from trades
        const maxPrice = Math.max(...this.trades.map(t => t.price))
        const minPrice = Math.min(...this.trades.map(t => t.price))
        const priceRange = maxPrice - minPrice

        if (priceRange === 0) {
            return Array(this.bucketNum).fill(0); // Handle no price variation
        }

        const sectionSize = priceRange / this.bucketNum

        // Create buckets with just totalValue
        const buckets = Array(this.bucketNum).fill(0)

        // Fill buckets (reverse order - higher prices first)
        this.trades.forEach(trade => {
            const normalBucketIndex = Math.min(Math.floor((trade.price - minPrice) / sectionSize), this.bucketNum - 1)
            const reversedBucketIndex = this.bucketNum - 1 - normalBucketIndex // Reverse the index
            buckets[reversedBucketIndex] += trade.qty
        })

        return buckets
    }

    get shape() {
        const volumes = this.distribution
        const n = volumes.length;
        if (n === 0) return 'none';

        const totalVolume = volumes.reduce((sum, v) => sum + v, 0);
        if (totalVolume === 0) return 'none';

        const meanVolume = totalVolume / n;
        const variance = volumes.reduce((sum, v) => sum + Math.pow(v - meanVolume, 2), 0) / (n - 1);
        const stdDev = Math.sqrt(variance);

        // Stricter HVN threshold: mean + 2 * stdDev
        const hvnThreshold = meanVolume + 2 * stdDev;
        const hvnBars = volumes
            .map((volume, index) => ({ index, volume }))
            .filter(bar => bar.volume > hvnThreshold);

        const thirdLength = (n - 1) / 3;

        if (hvnBars.length > 0) {
            const hvnWeightedSum = hvnBars.reduce((sum, bar) => sum + bar.index * bar.volume, 0);
            const hvnTotalVolume = hvnBars.reduce((sum, bar) => sum + bar.volume, 0);
            const hvnCenter = hvnWeightedSum / hvnTotalVolume;

            if (hvnCenter < thirdLength) {
                return 'p';
            } else if (hvnCenter > 2 * thirdLength) {
                return 'b';
            } else {
                return 'none';
            }
        } else {
            // Fallback: use overall volume-weighted center
            const weightedSum = volumes.reduce((sum, volume, index) => sum + index * volume, 0);
            const center = weightedSum / totalVolume;

            if (center < thirdLength) {
                return 'p';
            } else if (center > 2 * thirdLength) {
                return 'b';
            } else {
                return 'none';
            }
        }
    }
}