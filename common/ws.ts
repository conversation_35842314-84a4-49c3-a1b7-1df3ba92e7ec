import WebSocket from 'ws'

export class Ws {
    private socket?: WebSocket;
    public onOpen?: () => void;
    public onMessage?: (message: any) => void;
    public onReconnect?: () => void;
    private reconnecting = false;
    private reconnectAttempts = 0;
    private initialReconnectDelay = 3000;
    private maxReconnectDelay = 300000;
    private url: string;

    constructor(url: string) {
        this.url = url;
        this.connect()
    }

    private setupEventListeners() {
        this.socket?.on('open', () => {
            console.log('ws opened', this.url);
            this.reconnecting = false;
            this.reconnectAttempts = 0;
            this.onOpen?.();
        });

        this.socket?.on('message', (data) => {
            this.onMessage?.(JSON.parse(data.toString()));
        });

        this.socket?.on('close', async (code, reason) => {
            console.warn('ws closed:', code, reason);
            if (code === 1000) return
            await this.reconnect();
        });

        this.socket?.on('error', async (error) => {
            console.error('ws error:', error);
            await this.reconnect();
        });

        this.socket?.on('ping', (data) => {
            this.socket?.pong(data)
        });
    }

    private connect() {
        this.socket = new WebSocket(this.url, {
            perMessageDeflate: false
        });
        this.setupEventListeners();
    }

    send(payload: Record<string, any>) {
        if (this.socket?.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify(payload));
        } else {
            console.warn('ws cannot send: ws is not open');
        }
    }

    close() {
        if (this.socket && this.socket.readyState !== WebSocket.CLOSED) {
            this.socket.close();
        }
        this.socket = undefined
    }

    private async reconnect() {
        if (this.reconnecting) return;
        this.reconnecting = true;
        this.reconnectAttempts++;
        const delay = Math.min(
            this.initialReconnectDelay * Math.pow(2, this.reconnectAttempts - 1),
            this.maxReconnectDelay
        );
        console.log(`ws attempting to reconnect in ${delay}ms...`);
        await Bun.sleep(delay)
        this.close();
        this.connect();
        this.onReconnect?.();
        this.reconnecting = false;
    }

    get isOpen() {
        return this.socket?.readyState === WebSocket.OPEN
    }
}