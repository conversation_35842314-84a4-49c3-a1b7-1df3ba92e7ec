import { TPipelineData } from "../../types/pipeline.types";

export abstract class DataSource {
    protected name: string;
    protected callback?: (data: TPipelineData) => void;
    private healthCheckInterval: number = 60000;
    private healthCheckTimerId?: ReturnType<typeof setInterval>;

    constructor(name: string) {
        this.name = name;
        this.startHealthCheckTimer()
    }

    abstract connect(): Promise<void>;
    abstract disconnect(): Promise<void>;
    protected abstract checkConnectionHealth(): Promise<boolean> | boolean;

    protected async catchUp() { }
    async patchUp(...args: any[]) { }


    set onData(callback: (data: TPipelineData) => void) {
        this.callback = callback;
    }

    protected emitData(data: any, type?: string, extra?: { [key: string]: any }): void {
        if (this.callback) {
            this.callback({ data, type, extra });
        }
    }

    protected async reconnect(): Promise<void> {
        this.clearHealthCheckTimer()
        await this.disconnect();
        console.log(this.name, 'reconnecting in 5 seconds...');
        await Bun.sleep(5000)
        await this.connect();
        this.startHealthCheckTimer()
    }

    protected startHealthCheckTimer() {
        this.healthCheckTimerId = setInterval(async () => {
            const succes = await this.checkConnectionHealth();
            if (!succes) {
                console.log('pipeline', this.name, 'health check failed')
                await this.reconnect()
            }
        }, this.healthCheckInterval);
    }

    protected clearHealthCheckTimer() {
        if (this.healthCheckTimerId) {
            clearInterval(this.healthCheckTimerId);
            this.healthCheckTimerId = undefined;
        }
    }
}