import { TPipelineData } from "../../types/pipeline.types";

export abstract class DataConsumer {
    protected name: string;
    protected onInconsistentCallback?: (...args: any[]) => void;

    constructor(name: string) {
        this.name = name;
    }

    abstract handle(data: TPipelineData): void | Promise<void>;

    abstract saveProgress(data: TPipelineData): void | Promise<void>;

    set onInconsistent(callback: (...args: any[]) => void) {
        this.onInconsistentCallback = callback;
    }
}