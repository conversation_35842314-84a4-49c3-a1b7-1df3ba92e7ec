import { TPipelineData, TPipelineStage } from "../../types/pipeline.types";
import { DataSource } from "./source";
import { DataConsumer } from "./consumer";


export class Pipeline {
    private source: DataSource
    private stageList: TPipelineStage[] = []
    private name: string
    private consumer: DataConsumer
    private restarting = false

    constructor(name: string) {
        this.name = name
    }

    addSource(source: DataSource) {
        this.source = source
    }

    addStage(...stages: TPipelineStage[]) {
        this.stageList.push(...stages)
    }

    addConsumer(consumer: DataConsumer) {
        this.consumer = consumer
        this.consumer.onInconsistent = (...args: any[]) => {
            this.source.patchUp(...args)
        }
    }

    async start() {
        console.log('starting pipeline', this.name)
        if (!this.source) {
            throw new Error('No data source set')
        }
        this.source.onData = async (data) => {
            try {
                let processedData: any = data;
                for (const stage of this.stageList) {
                    processedData = await stage(processedData)
                }
                this.consumer?.handle(processedData)
            } catch (error) {
                console.error(this.name, 'pipeline error', error)
            }
        }
        await this.source.connect()
    }

    async stop() {
        console.log('stopping pipeline', this.name)
        await this.source.disconnect()
    }

    async restart() {
        if (this.restarting) return
        this.restarting = true
        await this.stop()
        console.log('restarting pipeline in 5 seconds...', this.name)
        await Bun.sleep(5000)
        await this.start()
        this.restarting = false
    }
}