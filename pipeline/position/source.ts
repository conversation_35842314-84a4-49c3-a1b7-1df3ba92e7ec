import { DataSource } from "../template/source";
import { fetchPositionList } from "../../exchange/coinbase";
import { SymbolList } from "../../common/constants";
import { findOpenPosition } from "../../database/tradeOrder";
import { retry } from "radash";


export class PositionSource extends DataSource {
    private intervalId?: ReturnType<typeof setInterval>;
    private prevHasOpenPosition = false

    constructor() {
        super('position')
    }

    async connect() {
        this.intervalId = setInterval(async () => {
            try {
                let hasOpenPosition = false
                for (const symbol of SymbolList) {
                    const position = findOpenPosition(symbol)

                    if (position) {
                        hasOpenPosition = true
                        break
                    }
                }
                if (!hasOpenPosition && !this.prevHasOpenPosition) return
                const result = await retry({ times: 2, delay: 200 }, fetchPositionList)
                this.prevHasOpenPosition = hasOpenPosition
                this.emitData(result)
            } catch (error) {
                console.error(this.name, 'error', error)
            }

        }, 2000);
    }

    protected async reconnect(): Promise<void> {
        await this.disconnect()
        console.log(this.name, 'reconnecting in 5 seconds...')
        await Bun.sleep(5000)
        await this.connect()
    }

    async disconnect() {
        console.log(this.name, 'disconnect')
        clearInterval(this.intervalId)
        this.intervalId = undefined
    }

    protected async checkConnectionHealth() {
        return this.intervalId !== undefined
    }
}