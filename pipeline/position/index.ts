import { Pipeline } from "../template/pipeline";
import { PositionSource } from './source';
import { PositionPipelineStages } from './stages'
import { PositionDataConsumer } from './consumer';

const positionPipeline = new Pipeline('position')
positionPipeline.addSource(new PositionSource())
positionPipeline.addStage(...PositionPipelineStages)
positionPipeline.addConsumer(new PositionDataConsumer())

export default positionPipeline