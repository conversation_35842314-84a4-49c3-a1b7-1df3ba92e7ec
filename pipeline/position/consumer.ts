import { type Wss } from "../../common/wss";
import { TPipelineData } from "../../types/pipeline.types";
import { DataConsumer } from "../template/consumer";
import { streamServer } from "../template/stream";

export class PositionDataConsumer extends DataConsumer {
    private wss: Wss

    constructor() {
        super('position')
        this.wss = streamServer
    }

    handle(data?: TPipelineData) {
        if (!data) return
        this.wss.pub(`position`, JSON.stringify({
            topic: `position`,
            data: data.data
        }))
    }
    saveProgress(data?: TPipelineData) { }
}