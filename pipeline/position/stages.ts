import { TPipelineData, TPipelineStage } from "../../types/pipeline.types";
import { withNull<PERSON>heck } from "../template/stages";

export const format: TPipelineStage = withNullCheck((data: TPipelineData) => {

    return {
        data: data.data.positions.map((p: any) => {
            const usdAmount = p.entry_vwap.value * p.net_size
            const pnl = parseFloat(p.aggregated_pnl.value)
            return {
                symbol: p.symbol.replace('-PERP-INTX', '').toLowerCase(),
                side: p.position_side.replace('POSITION_SIDE_', '').toLowerCase(),
                cryptoAmount: p.net_size,
                usdAmount: usdAmount.toFixed(2),
                unrealizedPnl: pnl.toFixed(2),
                pnlPct: (pnl / usdAmount).toFixed(4)
            }
        })
    }
})


export const filter: TPipelineStage = withNullCheck((data: TPipelineData) => {
    if (!data.data.positions) return null
    return data
})


export const PositionPipelineStages = [
    filter,
    format,
]