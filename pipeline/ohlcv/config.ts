import { SymbolList, TimeframeList } from '../../common/constants';
import settings from "../../settings";

const pairs: { symbol: string, timeframe: string }[] = []
for (const symbol of SymbolList) {
    for (const timeframe of TimeframeList) {
        pairs.push({ symbol, timeframe })
    }
}

const config = {
    ds: {
        pairs,
        wsUrl: settings.exchange.binance.wsUrl
    }
}
export default config

