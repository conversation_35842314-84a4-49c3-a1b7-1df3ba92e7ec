
import { Ws } from '../../common/ws';
import { DataSource } from "../template/source";
import config from "./config";
import { fetchKlines } from "../../exchange/binance";

export class OhlcvDataSource extends DataSource {
    private pairs: { symbol: string, timeframe: string }[]
    private ws?: Ws
    private lastMessageTime: number = Date.now()
    private inactiveTimeThreshold: number = 10000

    constructor() {
        super('ohlcv')
        this.pairs = config.ds.pairs
    }

    async connect() {
        if (this.ws) return

        this.ws = new Ws(config.ds.wsUrl)
        this.ws.onOpen = async () => {
            this.lastMessageTime = Date.now()
            await this.catchUp()
            this.ws?.send({
                method: 'SUBSCRIBE',
                params: this.pairs.map(p => `${p.symbol.toLowerCase() + 'usdt'}@kline_${p.timeframe}`),
                id: Date.now()
            })
        }

        this.ws.onMessage = (message: any) => {
            this.lastMessageTime = Date.now()
            this.emitData(message, 'ws')
        }
    }

    protected async reconnect(): Promise<void> {
        await this.disconnect()
        console.log('pipeline', this.name, 'reconnecting in 5 seconds...')
        await Bun.sleep(5000)
        await this.connect()
    }

    async disconnect() {
        console.log('pipeline', this.name, 'disconnect')
        this.ws?.close()
        this.ws = undefined
    }

    async catchUp() {
        console.log('pipeline', this.name, 'catching up')

        for (const { symbol, timeframe } of this.pairs) {
            const file = Bun.file(__dirname + '/checkpoint' + `/${symbol}-${timeframe}-checkpoint.txt`)
            if (!await file.exists()) continue
            let checkpoint = await file.text()
            // let startTime = parseInt(checkpoint) - 10 * TimeframeMillsMap[timeframe]
            let startTime = parseInt(checkpoint)
            let endTime = Date.now()

            while (true) {
                const klines = await fetchKlines({
                    symbol,
                    timeframe,
                    limit: 1000,
                    startTime,
                    endTime
                })
                console.log('pipeline', this.name, 'catching up', symbol, timeframe, 'klines', klines.length)
                klines.forEach(kline => {
                    this.emitData(kline, 'api', { symbol, timeframe })
                });

                if (klines.length < 1000) break

                startTime = klines[klines.length - 1][0] + 1 // Start from the next timestamp
            }
        }

    }

    protected async checkConnectionHealth() {
        if (!this.ws) return true // skip check when restarting
        return (this.ws.isOpen && (Date.now() - this.lastMessageTime) < this.inactiveTimeThreshold)
    }
}