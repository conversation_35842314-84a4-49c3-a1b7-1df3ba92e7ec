import { type Wss } from "../../common/wss";
import { TPipelineData } from "../../types/pipeline.types";
import { DataConsumer } from "../template/consumer";
import { upsertOhlcv } from "../../database/ohlcv";
import { streamServer } from "../template/stream";

export class OhlcvDataConsumer extends DataConsumer {
    private wss: Wss

    constructor() {
        super('ohlcv')
        this.wss = streamServer
    }

    async handle(data?: TPipelineData) {
        if (!data) return
        const { symbol, timeframe } = data.extra!
        upsertOhlcv(symbol, timeframe, data.data)
        this.saveProgress(data)
        this.wss.pub(`ohlcv`, JSON.stringify({
            topic: `ohlcv`,
            data: {
                ...data.data,
                symbol,
                timeframe
            }
        }))
    }
    saveProgress(data?: TPipelineData) {
        if (!data) return
        Bun.write(__dirname + '/checkpoint' + `/${data.extra!.symbol}-${data.extra!.timeframe}-checkpoint.txt`, data.data.time.toString(), { createPath: true })
    }
}