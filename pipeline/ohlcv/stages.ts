import { TPipelineData, TPipelineStage } from "../../types/pipeline.types";
import { with<PERSON><PERSON><PERSON><PERSON><PERSON> } from "../template/stages";

export const format: TPipelineStage = withNullCheck((data: TPipelineData) => {
    if (data.type === 'ws') {
        return {
            extra: {
                timeframe: data.data.k.i,
                symbol: data.data.s.toLowerCase().replace('usdt', '')
            },
            type: data.type,
            data: {
                time: data.data.k.t,
                open: parseFloat(data.data.k.o),
                high: parseFloat(data.data.k.h),
                low: parseFloat(data.data.k.l),
                close: parseFloat(data.data.k.c),
                volume: parseFloat(data.data.k.v),

            }
        }
    }
    else if (data.type === 'api') {
        return {
            extra: data.extra,
            type: data.type,
            data: {
                time: data.data[0],
                open: parseFloat(data.data[1]),
                high: parseFloat(data.data[2]),
                low: parseFloat(data.data[3]),
                close: parseFloat(data.data[4]),
                volume: parseFloat(data.data[5]),
            }
        }
    }
    return null
})

const filter: TPipelineStage = withNullCheck((data: TPipelineData) => {
    if (data.data.e === 'kline' && data.type === 'ws') return data
    else if (data.extra?.timeframe && data.extra?.symbol && data.type === 'api') return data
    else return null
})



export const OhlcvPipelineStages = [
    filter,
    format,
]