import { Pipeline } from "../template/pipeline";
import { OhlcvDataSource } from './source';
import { OhlcvPipelineStages } from './stages'
import { OhlcvDataConsumer } from './consumer';

const ohlcvPipeline = new Pipeline('ohlcv')
ohlcvPipeline.addSource(new OhlcvDataSource())
ohlcvPipeline.addStage(...OhlcvPipelineStages)
ohlcvPipeline.addConsumer(new OhlcvDataConsumer())

export default ohlcvPipeline