import { Pipeline } from "../template/pipeline";
import { AggTradeDataSource } from './source';
import { AggTradePipelineStages } from './stages'
import { AggTradeDataConsumer } from './consumer';


const aggTradePipeline = new Pipeline('aggTrade')
aggTradePipeline.addSource(new AggTradeDataSource())
aggTradePipeline.addStage(...AggTradePipelineStages)
aggTradePipeline.addConsumer(new AggTradeDataConsumer())

export default aggTradePipeline