
import { Ws } from '../../common/ws';
import { DataSource } from "../template/source";
import { fetchAggTrades } from "../../exchange/binance";
import config from "./config";
import { TAggTradeGap } from "../../types/aggTrade.types";

export class AggTradeDataSource extends DataSource {
    private ws?: Ws
    private lastMessageTime: number = Date.now()
    private inactiveTimeThreshold: number = 10000
    symbolList: string[];

    constructor() {
        super('aggTrade')
        this.symbolList = config.symbolList
    }

    async connect() {
        if (this.ws) return

        this.ws = new Ws(config.wsUrl)
        this.ws.onOpen = async () => {
            this.lastMessageTime = Date.now()
            this.ws?.send({
                "method": "SUBSCRIBE",
                "params": this.symbolList.map(s => `${s.toLowerCase()}usdt@aggTrade`),
                "id": Date.now()
            })
        }

        this.ws.onMessage = (message: any) => {
            this.lastMessageTime = Date.now()
            this.emitData(message, 'ws')
        }
    }

    protected async reconnect(): Promise<void> {
        await this.disconnect()
        console.log('pipeline', this.name, 'reconnecting in 5 seconds...')
        await Bun.sleep(5000)
        await this.connect()
    }

    async disconnect() {
        console.log('pipeline', this.name, 'disconnect')
        this.ws?.close()
        this.ws = undefined
    }

    async patchUp(gaps: TAggTradeGap[]) {
        for (const gap of gaps) {
            console.log('pipeline', this.name, 'patching up', gap.symbol)
            if (gap.time < (Date.now() - config.ttl)) continue
            await fetchAggTrades({
                symbol: gap.symbol,
                startId: gap.aggId,
                endId: gap.nextAggId,
                onData: (data) => {
                    data.forEach(aggTrade => {
                        this.emitData(aggTrade, 'api', { symbol: gap.symbol })
                    });
                }
            })
        }
    }


    protected async checkConnectionHealth() {
        if (!this.ws) return true // skip check when restarting
        return (this.ws.isOpen && (Date.now() - this.lastMessageTime) < this.inactiveTimeThreshold)
    }
}