import { TPipelineData, TPipelineStage } from "../../types/pipeline.types";
import { with<PERSON><PERSON><PERSON><PERSON><PERSON> } from "../template/stages";

export const format: TPipelineStage = withNullCheck((data: TPipelineData) => {
    if (data.type === 'ws') {
        return {
            extra: {
                symbol: data.data.s.toLowerCase().replace('usdt', '')
            },
            type: data.type,
            data: {
                aggId: data.data.a,
                price: parseFloat(data.data.p),
                qty: parseFloat(data.data.q),
                time: data.data.T,
                isMaker: data.data.m ? 1 : 0
            }
        }
    }
    else if (data.type === 'api') {
        return {
            extra: data.extra,
            type: data.type,
            data: {
                aggId: data.data.a,
                price: parseFloat(data.data.p),
                qty: parseFloat(data.data.q),
                time: data.data.T,
                isMaker: data.data.m ? 1 : 0
            }
        }
    }
    return null
})

const filter: TPipelineStage = withNull<PERSON>heck((data: TPipelineData) => {
    if (data.data.e === 'aggTrade' && data.type === 'ws') return data
    else if (data.extra?.symbol && data.type === 'api') return data
    else return null
})


export const AggTradePipelineStages = [
    filter,
    format,
]