
import { TPipelineData } from "../../types/pipeline.types";
import { DataConsumer } from "../template/consumer";
import { insertAggTrade, findGaps, deleteOldAggTrades } from "../../database/aggTrade";
import config from "./config";
import { SymbolList } from "../../common/constants";

export class AggTradeDataConsumer extends DataConsumer {
    gapCheckTimer: ReturnType<typeof setInterval>;
    purgeTimer: ReturnType<typeof setInterval>;
    syncTimer: ReturnType<typeof setInterval>;


    constructor() {
        super('aggTrade')
        this.gapCheckTimer = setInterval(() => {
            const gaps = findGaps()
            this.onInconsistentCallback?.(gaps)
        }, 10000)

        this.purgeTimer = setInterval(() => {
            for (const symbol of SymbolList) {
                const result = deleteOldAggTrades(symbol, config.ttl)
                console.log('pipeline', this.name, 'purged', result.deletedCount, 'old agg trades')
            }
        }, 5 * 60 * 1000)
    }

    async handle(data?: TPipelineData) {
        if (!data) return
        insertAggTrade({
            symbol: data.extra!.symbol,
            ...data.data
        })
    }

    saveProgress(data?: TPipelineData) { }
}